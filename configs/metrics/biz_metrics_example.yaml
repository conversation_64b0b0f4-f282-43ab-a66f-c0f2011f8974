# 业务指标配置示例（基于自动发现的指标）
# 这个文件展示了如何配置业务指标Dashboard

# Dashboard基础配置
name: "dubbo 调用"
refresh: "30s"
time_from: "now-1h"
time_to: "now"
tags:
  - "business"
  - "auto-generated"

# 数据源配置
datasource_uid: "fed52fm9xff9ca"

# 指标配置
metrics:
  # inbox业务指标
  - metric: "live_rpc_counter"
    type: "counter"
    desc: "收件箱请求"
    rowname: "请求指标"
    decimals: 1
    groupBy: ["instance", "method"]

  - metric: "dubbo_call"
    type: "timer"
    desc: "dubbo 调用"
    rowname: "性能指标"
    decimals: 2
    groupBy: ["instance", "method"]

  - metric: "biz_inbox_queue_sie"
    type: "gauge"
    desc: "收件箱队列大小"
    rowname: "系统状态"
    decimals: 0
    min_val: 0

  # common通用指标
  - metric: "group_session_first_page_unread_check_counter"
    type: "counter"
    desc: "groupsession"
    rowname: "inbox"
    min_val: 0