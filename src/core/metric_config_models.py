#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于指标的Dashboard配置模型
作者: yuangang
日期: 2025年7月
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from enum import Enum


class MetricType(Enum):
    """指标类型枚举"""
    COUNTER = "counter"
    GAUGE = "gauge"
    TIMER = "timer"        # Timer类型（Micrometer）


@dataclass
class MetricConfig:
    """单个指标配置"""
    metric: str                          # 指标名称
    type: MetricType                     # 指标类型
    desc: str                           # 描述
    rowname: str                        # 行名称

    # 可选配置
    unit: Optional[str] = None           # 单位
    min_val: Optional[float] = None      # 最小值
    max_val: Optional[float] = None      # 最大值
    decimals: Optional[int] = None       # 小数位数
    groupBy: Optional[List[str]] = None  # 分组字段

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MetricConfig':
        """从字典创建MetricConfig"""
        # 获取指标名称
        metric = data.get('metric')
        if not metric:
            raise ValueError("缺少指标名称字段 (metric)")

        # 转换指标类型为枚举
        type_str = data.get('type', '').lower()
        try:
            metric_type = MetricType(type_str)
        except ValueError:
            raise ValueError(
                f"不支持的指标类型: {type_str}. 支持的类型: {[t.value for t in MetricType]}")

        # 获取描述
        desc = data.get('desc')
        if not desc:
            raise ValueError("缺少描述字段 (desc)")

        # 获取行名称
        rowname = data.get('rowname')
        if not rowname:
            raise ValueError("缺少行名称字段 (rowname)")

        return cls(
            metric=metric,
            type=metric_type,
            desc=desc,
            rowname=rowname,
            unit=data.get('unit'),
            min_val=data.get('min_val'),
            max_val=data.get('max_val'),
            decimals=data.get('decimals'),
            groupBy=data.get('groupBy')
        )


@dataclass
class MetricDashboardConfig:
    """基于指标的Dashboard配置"""
    metrics: List[MetricConfig] = field(default_factory=list)

    # Dashboard基础配置
    name: Optional[str] = None           # Dashboard名称
    refresh: str = "30s"
    time_from: str = "now-1h"
    time_to: str = "now"
    tags: List[str] = field(default_factory=list)

    # 数据源配置
    datasource_uid: str = "prometheus"

    # 导入配置
    overwrite: bool = True

    def get_rows(self) -> Dict[str, List[MetricConfig]]:
        """按rowname分组指标"""
        rows = {}
        for metric in self.metrics:
            if metric.rowname not in rows:
                rows[metric.rowname] = []
            rows[metric.rowname].append(metric)
        return rows

    def dashboard_title(self) -> str:
        """生成Dashboard标题"""
        return self.name or "监控仪表盘"

    def dashboard_uid(self) -> str:
        """生成Dashboard UID"""
        if self.name:
            uid = self.name.lower().replace(' ', '-').replace('_', '-')
            return f"{uid}-dashboard"
        return "metrics-dashboard"

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MetricDashboardConfig':
        """从字典创建MetricDashboardConfig"""
        # 处理metrics
        metrics = []
        if 'metrics' in data:
            for metric_data in data['metrics']:
                metrics.append(MetricConfig.from_dict(metric_data))

        # 处理tags
        tags = data.get('tags', [])
        if isinstance(tags, str):
            tags = [tags]

        return cls(
            metrics=metrics,
            name=data.get('name'),
            refresh=data.get('refresh', '30s'),
            time_from=data.get('time_from', 'now-1h'),
            time_to=data.get('time_to', 'now'),
            tags=tags,
            datasource_uid=data.get('datasource_uid', 'prometheus'),
            overwrite=data.get('overwrite', True)
        )


@dataclass
class GeneratedPanelConfig:
    """生成的Panel配置"""
    title: str  # 面板标题
    expr: str  # 查询表达式
    panel_type: str = "timeseries"  # 面板类型
    unit: Optional[str] = None  # 单位
    min_val: Optional[float] = None  # 最小值
    max_val: Optional[float] = None  # 最大值
    decimals: Optional[int] = None  # 小数位数
    width: int = 6  # 面板宽度
    height: int = 6 # 面板高度
    groupBy: Optional[List[str]] = None  # 分组字段

    def to_panel_config_dict(self) -> Dict[str, Any]:
        """转换为PanelConfig字典格式""" 
        config = {
            'name': self.title.lower().replace(' ', '_').replace('(', '').replace(')', ''),
            'title': self.title,
            'type': self.panel_type,
            'query': self.expr,
            'width': self.width,
            'height': self.height
        }

        # 添加可选配置
        if self.unit:
            config['unit'] = self.unit
        if self.min_val is not None:
            config['min_val'] = self.min_val
        if self.max_val is not None:
            config['max_val'] = self.max_val
        if self.decimals is not None:
            config['decimals'] = self.decimals

        return config
