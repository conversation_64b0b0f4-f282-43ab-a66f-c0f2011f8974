#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指标信息存储管理器
作者: yuangang
日期: 2025年7月
"""

import os
import json
import yaml
from typing import Dict, List, Optional, Set
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

from prometheus_discovery import DiscoveredMetric

logger = logging.getLogger(__name__)


@dataclass
class StoredMetricInfo:
    """存储的指标信息"""
    name: str                    # 指标名称
    metric_type: str            # 指标类型
    help_text: str              # 帮助文本
    labels: List[str]           # 标签名称列表
    desc_label: Optional[str] = None  # desc标签的值
    category: Optional[str] = None    # 指标分类
    last_updated: Optional[str] = None  # 最后更新时间
    
    @classmethod
    def from_discovered_metric(cls, metric: DiscoveredMetric) -> 'StoredMetricInfo':
        """从发现的指标创建存储信息"""
        return cls(
            name=metric.name,
            metric_type=metric.metric_type,
            help_text=metric.help_text,
            labels=sorted(list(metric.labels)),
            desc_label=metric.desc_label,
            category=metric.category,
            last_updated=datetime.now().isoformat()
        )


@dataclass
class MetricStorageConfig:
    """指标存储配置"""
    storage_dir: str = "data/metrics"
    storage_format: str = "json"  # json 或 yaml
    backup_enabled: bool = True
    max_backups: int = 5


class MetricStorage:
    """指标信息存储管理器"""
    
    def __init__(self, config: MetricStorageConfig = None):
        """初始化存储管理器"""
        self.config = config or MetricStorageConfig()
        self.storage_dir = self.config.storage_dir
        
        # 确保存储目录存在
        os.makedirs(self.storage_dir, exist_ok=True)
        
        # 备份目录
        if self.config.backup_enabled:
            self.backup_dir = os.path.join(self.storage_dir, 'backups')
            os.makedirs(self.backup_dir, exist_ok=True)
    
    def save_metrics(self, metrics: Dict[str, List[DiscoveredMetric]], 
                    namespace: str = "default") -> str:
        """保存指标信息
        
        Args:
            metrics: 按分类分组的指标字典
            namespace: 命名空间
            
        Returns:
            保存的文件路径
        """
        try:
            # 转换为存储格式
            stored_data = {
                'namespace': namespace,
                'last_updated': datetime.now().isoformat(),
                'categories': {}
            }
            
            for category, metric_list in metrics.items():
                stored_metrics = []
                for metric in metric_list:
                    stored_metric = StoredMetricInfo.from_discovered_metric(metric)
                    stored_metrics.append(asdict(stored_metric))
                
                stored_data['categories'][category] = {
                    'metrics': stored_metrics,
                    'count': len(stored_metrics)
                }
            
            # 生成文件名
            filename = f"metrics_{namespace}.{self.config.storage_format}"
            filepath = os.path.join(self.storage_dir, filename)
            
            # 备份现有文件
            if self.config.backup_enabled and os.path.exists(filepath):
                self._backup_file(filepath)
            
            # 保存文件
            if self.config.storage_format == 'json':
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(stored_data, f, indent=2, ensure_ascii=False)
            else:  # yaml
                with open(filepath, 'w', encoding='utf-8') as f:
                    yaml.dump(stored_data, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
            
            logger.info(f"指标信息已保存到: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"保存指标信息失败: {e}")
            raise
    
    def load_metrics(self, namespace: str = "default") -> Optional[Dict[str, List[StoredMetricInfo]]]:
        """加载指标信息
        
        Args:
            namespace: 命名空间
            
        Returns:
            按分类分组的指标字典，如果文件不存在返回None
        """
        try:
            filename = f"metrics_{namespace}.{self.config.storage_format}"
            filepath = os.path.join(self.storage_dir, filename)
            
            if not os.path.exists(filepath):
                logger.info(f"指标文件不存在: {filepath}")
                return None
            
            # 加载文件
            if self.config.storage_format == 'json':
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            else:  # yaml
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
            
            # 转换为StoredMetricInfo对象
            result = {}
            for category, category_data in data.get('categories', {}).items():
                metrics = []
                for metric_data in category_data.get('metrics', []):
                    metric = StoredMetricInfo(**metric_data)
                    metrics.append(metric)
                result[category] = metrics
            
            logger.info(f"从 {filepath} 加载了 {len(result)} 个分类的指标信息")
            return result
            
        except Exception as e:
            logger.error(f"加载指标信息失败: {e}")
            raise
    
    def merge_metrics(self, existing: Dict[str, List[StoredMetricInfo]], 
                     new_metrics: Dict[str, List[DiscoveredMetric]]) -> Dict[str, List[StoredMetricInfo]]:
        """合并现有指标和新发现的指标
        
        Args:
            existing: 现有的指标信息
            new_metrics: 新发现的指标
            
        Returns:
            合并后的指标信息
        """
        result = existing.copy() if existing else {}
        
        for category, metric_list in new_metrics.items():
            if category not in result:
                result[category] = []
            
            # 创建现有指标名称集合
            existing_names = {m.name for m in result[category]}
            
            # 添加新指标
            for metric in metric_list:
                if metric.name not in existing_names:
                    stored_metric = StoredMetricInfo.from_discovered_metric(metric)
                    result[category].append(stored_metric)
                else:
                    # 更新现有指标的信息
                    for i, existing_metric in enumerate(result[category]):
                        if existing_metric.name == metric.name:
                            # 更新标签和描述信息
                            updated_metric = StoredMetricInfo.from_discovered_metric(metric)
                            result[category][i] = updated_metric
                            break
        
        return result
    
    def _backup_file(self, filepath: str):
        """备份文件"""
        try:
            import shutil
            
            filename = os.path.basename(filepath)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{timestamp}_{filename}"
            backup_path = os.path.join(self.backup_dir, backup_filename)
            
            shutil.copy2(filepath, backup_path)
            
            # 清理旧备份
            self._cleanup_old_backups()
            
        except Exception as e:
            logger.warning(f"备份文件失败: {e}")
    
    def _cleanup_old_backups(self):
        """清理旧备份文件"""
        try:
            if not os.path.exists(self.backup_dir):
                return
            
            # 获取所有备份文件
            backup_files = []
            for filename in os.listdir(self.backup_dir):
                filepath = os.path.join(self.backup_dir, filename)
                if os.path.isfile(filepath):
                    backup_files.append((filepath, os.path.getmtime(filepath)))
            
            # 按修改时间排序
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # 删除超出限制的备份
            if len(backup_files) > self.config.max_backups:
                for filepath, _ in backup_files[self.config.max_backups:]:
                    os.remove(filepath)
                    logger.info(f"删除旧备份: {filepath}")
                    
        except Exception as e:
            logger.warning(f"清理备份失败: {e}")
    
    def list_stored_metrics(self, namespace: str = "default") -> Dict[str, int]:
        """列出存储的指标统计信息
        
        Args:
            namespace: 命名空间
            
        Returns:
            分类和指标数量的字典
        """
        metrics = self.load_metrics(namespace)
        if not metrics:
            return {}
        
        return {category: len(metric_list) for category, metric_list in metrics.items()}
    
    def get_metric_by_name(self, metric_name: str, 
                          namespace: str = "default") -> Optional[StoredMetricInfo]:
        """根据名称获取指标信息
        
        Args:
            metric_name: 指标名称
            namespace: 命名空间
            
        Returns:
            指标信息，如果不存在返回None
        """
        metrics = self.load_metrics(namespace)
        if not metrics:
            return None
        
        for category, metric_list in metrics.items():
            for metric in metric_list:
                if metric.name == metric_name:
                    return metric
        
        return None


def main():
    """测试函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='指标存储管理器')
    parser.add_argument('--action', choices=['list', 'show'], 
                       default='list', help='操作类型')
    parser.add_argument('--namespace', default='default', help='命名空间')
    parser.add_argument('--metric-name', help='指标名称（用于show操作）')
    
    args = parser.parse_args()
    
    storage = MetricStorage()
    
    if args.action == 'list':
        stats = storage.list_stored_metrics(args.namespace)
        if stats:
            print(f"命名空间 '{args.namespace}' 的指标统计:")
            for category, count in stats.items():
                print(f"  {category}: {count} 个指标")
        else:
            print(f"命名空间 '{args.namespace}' 没有存储的指标")
    
    elif args.action == 'show':
        if not args.metric_name:
            print("错误: --metric-name 参数是必需的")
            return 1
        
        metric = storage.get_metric_by_name(args.metric_name, args.namespace)
        if metric:
            print(f"指标信息:")
            print(f"  名称: {metric.name}")
            print(f"  类型: {metric.metric_type}")
            print(f"  描述: {metric.help_text}")
            print(f"  分类: {metric.category}")
            if metric.desc_label:
                print(f"  Desc标签: {metric.desc_label}")
            print(f"  标签: {', '.join(metric.labels)}")
            print(f"  最后更新: {metric.last_updated}")
        else:
            print(f"未找到指标: {args.metric_name}")
    
    return 0


if __name__ == "__main__":
    exit(main())
