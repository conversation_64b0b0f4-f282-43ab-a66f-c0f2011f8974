#!/usr/bin/env python3
"""
调试Grafana文件夹结构的脚本
用于分析为什么嵌套子目录没有被正确创建
"""

import re
import zipfile
import datetime
import logging
import requests
import sys
import os
import json

# --- 动态添加项目根目录到 sys.path ---
# 这样做可以确保无论从哪里运行脚本，都能正确找到 src 模块
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(script_dir, '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.core.grafana_api import GrafanaAPI, GrafanaFolder

# --- 日志配置 ---
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')

# --- 配置 ---
GRAFANA_URL = os.getenv('GRAFANA_URL', 'https://grafana.finkapp.cn')


def debug_folder_structure(api_client: GrafanaAPI):
    """调试文件夹结构"""
    print("=== 开始调试Grafana文件夹结构 ===\n")
    
    # 1. 获取所有文件夹
    all_folders = api_client.list_folders()
    if not all_folders:
        print("❌ 未在Grafana中找到任何文件夹。")
        return
    
    print(f"📁 总共找到 {len(all_folders)} 个文件夹\n")
    
    # 2. 详细打印每个文件夹的信息
    print("--- 文件夹详细信息 ---")
    for i, folder in enumerate(all_folders, 1):
        parent_info = f"父文件夹UID: {folder.parent_uid}" if folder.parent_uid else "根文件夹"
        print(f"{i}. 标题: '{folder.title}'")
        print(f"   UID: {folder.uid}")
        print(f"   ID: {folder.id}")
        print(f"   {parent_info}")
        print(f"   URL: {folder.url}")
        print()
    
    # 3. 分析父子关系
    print("--- 父子关系分析 ---")
    folder_map = {f.uid: f for f in all_folders}
    root_folders = [f for f in all_folders if f.parent_uid is None]
    child_folders = [f for f in all_folders if f.parent_uid is not None]
    
    print(f"根文件夹数量: {len(root_folders)}")
    for folder in root_folders:
        print(f"  - {folder.title} (UID: {folder.uid})")
    
    print(f"\n子文件夹数量: {len(child_folders)}")
    for folder in child_folders:
        parent_folder = folder_map.get(folder.parent_uid)
        parent_title = parent_folder.title if parent_folder else "未知"
        print(f"  - {folder.title} (UID: {folder.uid}) -> 父文件夹: {parent_title} ({folder.parent_uid})")
    
    # 4. 检查特定的"随便玩玩"和"test"文件夹
    print("\n--- 特定文件夹检查 ---")
    casual_folder = None
    test_folder = None
    
    for folder in all_folders:
        if "随便玩玩" in folder.title:
            casual_folder = folder
            print(f"✅ 找到'随便玩玩'文件夹: {folder.title} (UID: {folder.uid})")
        if "test" in folder.title.lower():
            test_folder = folder
            print(f"✅ 找到'test'文件夹: {folder.title} (UID: {folder.uid})")
            if folder.parent_uid:
                parent = folder_map.get(folder.parent_uid)
                parent_name = parent.title if parent else "未知"
                print(f"   父文件夹: {parent_name} (UID: {folder.parent_uid})")
    
    if not casual_folder:
        print("❌ 未找到'随便玩玩'文件夹")
    if not test_folder:
        print("❌ 未找到'test'文件夹")
    
    # 5. 测试路径构建
    print("\n--- 路径构建测试 ---")
    path_map = build_folder_paths(all_folders)
    
    print("构建的路径映射:")
    for uid, path in path_map.items():
        folder = folder_map[uid]
        print(f"  {folder.title} (UID: {uid}) -> {path}")
    
    # 6. 检查是否有嵌套路径
    print("\n--- 嵌套路径检查 ---")
    nested_found = False
    for uid, path in path_map.items():
        if os.sep in path:  # 包含路径分隔符，说明是嵌套的
            print(f"✅ 嵌套路径: {path}")
            nested_found = True
    
    if not nested_found:
        print("❌ 未找到任何嵌套路径")
    
    return all_folders, path_map


def build_folder_paths(all_folders):
    """构建文件夹路径映射"""
    folder_details_map = {f.uid: f for f in all_folders}
    path_map = {}

    def _build_path(folder_uid, visited=None):
        """递归构建文件夹路径，使用visited集合避免循环引用"""
        if visited is None:
            visited = set()
        
        if folder_uid is None or folder_uid not in folder_details_map:
            return []
        
        # 检查循环引用
        if folder_uid in visited:
            print(f"⚠️  检测到文件夹循环引用: {folder_uid}")
            return []
        
        visited.add(folder_uid)
        folder = folder_details_map[folder_uid]
        parent_uid = folder.parent_uid

        # 如果是根文件夹（没有父文件夹）
        if parent_uid is None:
            return [folder.title]
        else:
            # 递归获取父路径，然后添加当前文件夹
            parent_path_parts = _build_path(parent_uid, visited.copy())
            return parent_path_parts + [folder.title]

    for folder in all_folders:
        folder_uid = folder.uid
        full_path_parts = _build_path(folder_uid)
        if full_path_parts:  # 只有成功构建路径的文件夹才添加到映射中
            path_map[folder_uid] = os.path.join(*full_path_parts)
        else:
            print(f"⚠️  无法构建文件夹路径: {folder.title} (UID: {folder_uid})")

    return path_map


def main():
    """主执行函数"""
    print("🔍 开始调试Grafana文件夹结构...")
    
    # 获取 Grafana API Token
    grafana_token = os.getenv('GRAFANA_TOKEN', 'glsa_M5nAWArffjHCLAnKrszFJgRNnZaB4BBB_7ec2cb79')
    
    if not grafana_token:
        print("❌ 错误：未找到 GRAFANA_TOKEN 配置。")
        return
    
    # 初始化 Grafana API 客户端
    api = GrafanaAPI(GRAFANA_URL, grafana_token)
    success, message = api.test_connection()
    if not success:
        print(f"❌ Grafana API 连接失败: {message}")
        return
    
    print(f"✅ 成功连接到 Grafana: {GRAFANA_URL}")
    
    # 调试文件夹结构
    folders, path_map = debug_folder_structure(api)
    
    print(f"\n=== 调试完成 ===")
    print(f"总文件夹数: {len(folders)}")
    print(f"路径映射数: {len(path_map)}")


if __name__ == '__main__':
    main()
