#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动发现脚本 - 简化版本
作者: yuangang
日期: 2025年7月

这是一个简化的自动发现脚本，替代了复杂的原版本。
无复杂依赖，易于调试和使用。
"""

import os
import sys

# 添加tools目录到路径，使用tools中的简化工具
current_dir = os.path.dirname(__file__)
project_root = os.path.dirname(current_dir)
tools_dir = os.path.join(project_root, 'tools')
sys.path.insert(0, tools_dir)

# 导入简化的自动发现工具
try:
    from auto_discovery_simple import main as simple_main
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保 tools/auto_discovery_simple.py 文件存在")
    sys.exit(1)


def main():
    """主函数 - 直接调用简化版本"""
    print("🚀 自动发现脚本 (简化版本)")
    print("=" * 50)
    print("这是一个简化的自动发现工具，无复杂依赖，易于使用。")
    print()
    
    # 直接调用简化版本的main函数
    return simple_main()


if __name__ == "__main__":
    exit(main())
