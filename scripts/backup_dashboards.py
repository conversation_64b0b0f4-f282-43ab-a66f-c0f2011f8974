import re
import zipfile
import datetime
import logging
import requests
from qcloud_cos import CosConfig, CosS3Client
import sys
import os
import json

# --- 动态添加项目根目录到 sys.path ---
# 这样做可以确保无论从哪里运行脚本，都能正确找到 src 模块
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(script_dir, '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.core.grafana_api import GrafanaAPI, GrafanaFolder


# --- 配置 ---
GRAFANA_URL = os.getenv('GRAFANA_URL', 'https://grafana.finkapp.cn')
COS_SECRET_ID = "AKIDufAPTHqAE4ex0nKaZHFzSebJlx0sYx2r"
COS_SECRET_KEY = "auxlJaaLq42jBNJyNWP2QYPzIL9gwwhV"
COS_REGION = 'ap-shanghai'
COS_BUCKET = 'finka-ops-1251485948'

# --- 日志配置 ---
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')


def save_dashboard_locally(api_client: GrafanaAPI, uid: str, full_local_path: str):
    """获取并保存单个仪表盘的 JSON 定义到指定完整路径。"""
    try:
        dashboard_data = api_client.get_dashboard_by_uid(uid)
        if not dashboard_data:
            logging.error(f"获取仪表盘 '{uid}' 失败: 未找到或API错误")
            return None

        # 确保本地路径存在
        os.makedirs(full_local_path, exist_ok=True)

        # 仪表盘标题可能包含特殊字符，需要清理
        title = dashboard_data['dashboard']['title']
        sanitized_title = re.sub(r'[\\/:*?"<>|]', '', title)  # 移除文件名不允许的字符
        file_name = f"{sanitized_title}.json"
        file_path = os.path.join(full_local_path, file_name)

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(dashboard_data, f, ensure_ascii=False, indent=2)
        logging.info(f"仪表盘 '{title}' (UID: {uid}) 已保存到 {file_path}")
        return file_path
    except requests.exceptions.RequestException as e:
        logging.error(f"获取或保存仪表盘 '{uid}' 失败: {e}")
        return None


def create_zip_archive(source_dir, output_zip_path):
    """将指定目录下的所有内容打包成ZIP文件。"""
    logging.info(f"开始创建ZIP归档文件: {output_zip_path}")
    try:
        with zipfile.ZipFile(output_zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(source_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    # 计算文件在ZIP中的相对路径
                    arcname = os.path.relpath(file_path, source_dir)
                    zipf.write(file_path, arcname)
        logging.info(f"成功创建ZIP归档文件: {output_zip_path}")
        return True
    except Exception as e:
        logging.error(f"创建ZIP归档文件失败: {e}")
        return False


def upload_to_cos(cos_client, file_path):
    """上传文件到 COS。"""
    # 构造COS上的路径，使用文件名作为key，并添加前缀
    cos_key = f"grafana_backups/{os.path.basename(file_path)}"

    try:
        cos_client.upload_file(Bucket=COS_BUCKET,
                               LocalFilePath=file_path,
                               Key=cos_key,
                               EnableMD5=False)
        logging.info(f"已成功上传 {file_path} 到 COS 存储桶 {COS_BUCKET}，路径为 {cos_key}")
    except Exception as e:
        logging.error(f"上传文件 {file_path} 到 COS 失败: {e}")


def get_folder_path_map(api_client: GrafanaAPI):
    """获取所有文件夹并构建 folderId -> full_path 的映射。"""
    all_folders = api_client.list_folders()
    if not all_folders:
        logging.warning("未在Grafana中找到任何文件夹。")
        return {}

    # 构建文件夹路径映射
    folder_details_map = {f.uid: f for f in all_folders}
    path_map = {}

    def _build_path(folder_uid, visited=None):
        """递归构建文件夹路径，使用visited集合避免循环引用"""
        if visited is None:
            visited = set()

        if folder_uid is None or folder_uid not in folder_details_map:
            return []

        # 检查循环引用
        if folder_uid in visited:
            logging.warning(f"检测到文件夹循环引用: {folder_uid}")
            return []

        visited.add(folder_uid)
        folder = folder_details_map[folder_uid]
        parent_uid = folder.parent_uid

        # 如果是根文件夹（没有父文件夹）
        if parent_uid is None:
            return [folder.title]
        else:
            # 递归获取父路径，然后添加当前文件夹
            parent_path_parts = _build_path(parent_uid, visited.copy())
            return parent_path_parts + [folder.title]

    for folder in all_folders:
        folder_uid = folder.uid
        full_path_parts = _build_path(folder_uid)
        if full_path_parts:  # 只有成功构建路径的文件夹才添加到映射中
            path_map[folder_uid] = os.path.join(*full_path_parts)
        else:
            logging.warning(f"无法构建文件夹路径: {folder.title} (UID: {folder_uid})")

    return path_map


def get_all_dashboards(api_client: GrafanaAPI):
    """从 Grafana API 获取所有仪表盘的列表。"""
    return api_client.search_dashboards()


def main():
    """主执行函数"""
    logging.info("开始执行 Grafana 仪表盘备份任务...")

    # 获取 Grafana API Token，可以从环境变量或直接硬编码
    # 建议从环境变量获取，以提高安全性
    grafana_token = os.getenv(
        'GRAFANA_TOKEN', 'glsa_M5nAWArffjHCLAnKrszFJgRNnZaB4BBB_7ec2cb79')  # 替换为你的实际Token

    # 检查配置是否完整
    if not all([grafana_token, COS_SECRET_ID, COS_SECRET_KEY, COS_BUCKET]):
        logging.error("错误：脚本中的一个或多个密钥配置不完整。")
        return

    # 初始化 Grafana API 客户端
    api = GrafanaAPI(GRAFANA_URL, grafana_token)
    success, message = api.test_connection()
    if not success:
        logging.error(f"Grafana API 连接失败: {message}")
        return

    # 初始化 COS 客户端
    try:
        config = CosConfig(Region=COS_REGION,
                           SecretId=COS_SECRET_ID, SecretKey=COS_SECRET_KEY)
        cos_client = CosS3Client(config)
    except Exception as e:
        logging.error(f"初始化 COS 客户端失败: {e}")
        return

    # 创建带时间戳的备份目录
    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    global BACKUP_DIR  # 声明为全局变量，以便其他函数使用
    BACKUP_DIR = os.path.join(os.path.dirname(
        __file__), '..', 'output', 'dashboard_backups', timestamp)
    os.makedirs(BACKUP_DIR, exist_ok=True)
    logging.info(f"本次备份目录: {BACKUP_DIR}")

    # 1. 获取文件夹路径映射
    folder_map = get_folder_path_map(api)
    if folder_map is None:
        logging.error("无法获取文件夹映射，任务终止。")
        return
    logging.info(f"成功构建文件夹路径映射: {len(folder_map)}个文件夹")

    # 2. 获取所有仪表盘
    all_dashboards = get_all_dashboards(api)
    if not all_dashboards:
        logging.warning("未在Grafana中找到任何仪表盘。")
        return

    successful_backups = 0
    for dash in all_dashboards:
        folder_uid = dash.folderUid
        # 从映射中获取完整路径，如果仪表盘在根目录(General)，则folder_uid为None
        relative_path = folder_map.get(folder_uid, '') if folder_uid else ''
        full_local_path = os.path.join(BACKUP_DIR, relative_path)

        # 保存到本地
        local_file_path = save_dashboard_locally(
            api_client=api, uid=dash.uid, full_local_path=full_local_path)

        # 统计成功备份的仪表盘数量
        if local_file_path:
            successful_backups += 1

    total_backups = successful_backups
    logging.info(f"仪表盘本地备份完成。共成功备份 {total_backups} 个仪表盘到 {BACKUP_DIR}。")

    # 打包备份目录
    zip_file_name = f"grafana_dashboards_backup_{timestamp}.zip"
    zip_file_path = os.path.join(os.path.dirname(BACKUP_DIR), zip_file_name)
    if create_zip_archive(BACKUP_DIR, zip_file_path):
        upload_to_cos(cos_client, zip_file_path)
    else:
        logging.error("打包备份文件失败，跳过COS上传。")

    logging.info("备份任务完成。")


if __name__ == '__main__':
    main()
