#!/usr/bin/env python3
"""
简单的Grafana文件夹结构调试脚本
直接使用requests调用API，不依赖其他模块
"""

import requests
import json
import os
from typing import Dict, List, Optional
from dataclasses import dataclass


@dataclass
class SimpleFolder:
    """简单的文件夹数据结构"""
    id: int
    uid: str
    title: str
    parent_uid: Optional[str] = None


def get_grafana_folders(base_url: str, api_key: str) -> List[SimpleFolder]:
    """直接调用Grafana API获取文件夹列表"""
    url = f"{base_url.rstrip('/')}/api/folders"
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        folders_data = response.json()
        folders = []
        
        for folder_data in folders_data:
            folder = SimpleFolder(
                id=folder_data.get('id'),
                uid=folder_data.get('uid'),
                title=folder_data.get('title'),
                parent_uid=folder_data.get('parentUid')  # 注意这里可能是parentUid而不是parent_uid
            )
            folders.append(folder)
        
        return folders
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 获取文件夹失败: {e}")
        return []


def build_folder_paths(all_folders: List[SimpleFolder]) -> Dict[str, str]:
    """构建文件夹路径映射"""
    folder_details_map = {f.uid: f for f in all_folders}
    path_map = {}

    def _build_path(folder_uid, visited=None):
        """递归构建文件夹路径，使用visited集合避免循环引用"""
        if visited is None:
            visited = set()
        
        if folder_uid is None or folder_uid not in folder_details_map:
            return []
        
        # 检查循环引用
        if folder_uid in visited:
            print(f"⚠️  检测到文件夹循环引用: {folder_uid}")
            return []
        
        visited.add(folder_uid)
        folder = folder_details_map[folder_uid]
        parent_uid = folder.parent_uid

        # 如果是根文件夹（没有父文件夹）
        if parent_uid is None:
            return [folder.title]
        else:
            # 递归获取父路径，然后添加当前文件夹
            parent_path_parts = _build_path(parent_uid, visited.copy())
            return parent_path_parts + [folder.title]

    for folder in all_folders:
        folder_uid = folder.uid
        full_path_parts = _build_path(folder_uid)
        if full_path_parts:  # 只有成功构建路径的文件夹才添加到映射中
            path_map[folder_uid] = os.path.join(*full_path_parts)
        else:
            print(f"⚠️  无法构建文件夹路径: {folder.title} (UID: {folder_uid})")

    return path_map


def debug_folder_structure():
    """调试文件夹结构"""
    print("=== 开始调试Grafana文件夹结构 ===\n")
    
    # 配置
    GRAFANA_URL = os.getenv('GRAFANA_URL', 'https://grafana.finkapp.cn')
    grafana_token = os.getenv('GRAFANA_TOKEN', 'glsa_M5nAWArffjHCLAnKrszFJgRNnZaB4BBB_7ec2cb79')
    
    if not grafana_token:
        print("❌ 错误：未找到 GRAFANA_TOKEN 配置。")
        return
    
    print(f"🔗 连接到 Grafana: {GRAFANA_URL}")
    
    # 1. 获取所有文件夹
    all_folders = get_grafana_folders(GRAFANA_URL, grafana_token)
    if not all_folders:
        print("❌ 未在Grafana中找到任何文件夹。")
        return
    
    print(f"📁 总共找到 {len(all_folders)} 个文件夹\n")
    
    # 2. 详细打印每个文件夹的信息
    print("--- 文件夹详细信息 ---")
    for i, folder in enumerate(all_folders, 1):
        parent_info = f"父文件夹UID: {folder.parent_uid}" if folder.parent_uid else "根文件夹"
        print(f"{i}. 标题: '{folder.title}'")
        print(f"   UID: {folder.uid}")
        print(f"   ID: {folder.id}")
        print(f"   {parent_info}")
        print()
    
    # 3. 分析父子关系
    print("--- 父子关系分析 ---")
    folder_map = {f.uid: f for f in all_folders}
    root_folders = [f for f in all_folders if f.parent_uid is None]
    child_folders = [f for f in all_folders if f.parent_uid is not None]
    
    print(f"根文件夹数量: {len(root_folders)}")
    for folder in root_folders:
        print(f"  - {folder.title} (UID: {folder.uid})")
    
    print(f"\n子文件夹数量: {len(child_folders)}")
    for folder in child_folders:
        parent_folder = folder_map.get(folder.parent_uid)
        parent_title = parent_folder.title if parent_folder else "未知"
        print(f"  - {folder.title} (UID: {folder.uid}) -> 父文件夹: {parent_title} ({folder.parent_uid})")
    
    # 4. 检查特定的"随便玩玩"和"test"文件夹
    print("\n--- 特定文件夹检查 ---")
    casual_folder = None
    test_folder = None
    
    for folder in all_folders:
        if "随便玩玩" in folder.title:
            casual_folder = folder
            print(f"✅ 找到'随便玩玩'文件夹: {folder.title} (UID: {folder.uid})")
        if "test" in folder.title.lower():
            test_folder = folder
            print(f"✅ 找到'test'文件夹: {folder.title} (UID: {folder.uid})")
            if folder.parent_uid:
                parent = folder_map.get(folder.parent_uid)
                parent_name = parent.title if parent else "未知"
                print(f"   父文件夹: {parent_name} (UID: {folder.parent_uid})")
    
    if not casual_folder:
        print("❌ 未找到'随便玩玩'文件夹")
    if not test_folder:
        print("❌ 未找到'test'文件夹")
    
    # 5. 测试路径构建
    print("\n--- 路径构建测试 ---")
    path_map = build_folder_paths(all_folders)
    
    print("构建的路径映射:")
    for uid, path in path_map.items():
        folder = folder_map[uid]
        print(f"  {folder.title} (UID: {uid}) -> {path}")
    
    # 6. 检查是否有嵌套路径
    print("\n--- 嵌套路径检查 ---")
    nested_found = False
    for uid, path in path_map.items():
        if os.sep in path:  # 包含路径分隔符，说明是嵌套的
            print(f"✅ 嵌套路径: {path}")
            nested_found = True
    
    if not nested_found:
        print("❌ 未找到任何嵌套路径")
        print("   这可能意味着：")
        print("   1. Grafana中确实没有嵌套的文件夹结构")
        print("   2. API返回的parent_uid字段为空或不正确")
        print("   3. 文件夹的父子关系在Grafana中没有正确设置")
    
    print(f"\n=== 调试完成 ===")
    print(f"总文件夹数: {len(all_folders)}")
    print(f"路径映射数: {len(path_map)}")
    
    return all_folders, path_map


if __name__ == '__main__':
    debug_folder_structure()
